/**
 * 開發環境專用日誌工具
 * 
 * 提供全局的console.log包裝器，只在開發環境中輸出日誌
 * 生產環境中會自動禁用所有console輸出
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * 檢查是否為開發環境
 */
const isDevelopment = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

/**
 * 開發環境專用console.log包裝器
 * 只在開發環境中輸出日誌，生產環境中不會有任何輸出
 * 
 * @param args - 要輸出的參數
 * 
 * @example
 * ```typescript
 * import { devLog } from '@/utils/devLogger';
 * 
 * devLog('這是開發環境的日誌');
 * devLog('用戶資料:', userData);
 * devLog('API回應:', response, '狀態:', status);
 * ```
 */
export const devLog = (...args: any[]): void => {
  if (isDevelopment()) {
    console.log(...args);
  }
};

/**
 * 開發環境專用console.error包裝器
 * 只在開發環境中輸出錯誤日誌，生產環境中不會有任何輸出
 * 
 * @param args - 要輸出的參數
 * 
 * @example
 * ```typescript
 * import { devError } from '@/utils/devLogger';
 * 
 * devError('API錯誤:', error);
 * devError('表單驗證失敗:', validationErrors);
 * ```
 */
export const devError = (...args: any[]): void => {
  if (isDevelopment()) {
    console.error(...args);
  }
};

/**
 * 開發環境專用console.warn包裝器
 * 只在開發環境中輸出警告日誌，生產環境中不會有任何輸出
 * 
 * @param args - 要輸出的參數
 * 
 * @example
 * ```typescript
 * import { devWarn } from '@/utils/devLogger';
 * 
 * devWarn('已棄用的API:', apiName);
 * devWarn('性能警告:', performanceData);
 * ```
 */
export const devWarn = (...args: any[]): void => {
  if (isDevelopment()) {
    console.warn(...args);
  }
};

/**
 * 開發環境專用console.info包裝器
 * 只在開發環境中輸出資訊日誌，生產環境中不會有任何輸出
 * 
 * @param args - 要輸出的參數
 * 
 * @example
 * ```typescript
 * import { devInfo } from '@/utils/devLogger';
 * 
 * devInfo('應用程式啟動完成');
 * devInfo('載入模組:', moduleName);
 * ```
 */
export const devInfo = (...args: any[]): void => {
  if (isDevelopment()) {
    console.info(...args);
  }
};

/**
 * 開發環境專用console.debug包裝器
 * 只在開發環境中輸出除錯日誌，生產環境中不會有任何輸出
 * 
 * @param args - 要輸出的參數
 * 
 * @example
 * ```typescript
 * import { devDebug } from '@/utils/devLogger';
 * 
 * devDebug('除錯資訊:', debugData);
 * devDebug('函數執行時間:', executionTime);
 * ```
 */
export const devDebug = (...args: any[]): void => {
  if (isDevelopment()) {
    console.debug(...args);
  }
};

/**
 * 開發環境專用console.table包裝器
 * 只在開發環境中輸出表格日誌，生產環境中不會有任何輸出
 * 
 * @param data - 要以表格形式顯示的資料
 * @param columns - 可選的欄位名稱
 * 
 * @example
 * ```typescript
 * import { devTable } from '@/utils/devLogger';
 * 
 * devTable(users);
 * devTable(products, ['name', 'price', 'category']);
 * ```
 */
export const devTable = (data: any, columns?: string[]): void => {
  if (isDevelopment()) {
    console.table(data, columns);
  }
};

/**
 * 開發環境專用console.group包裝器
 * 只在開發環境中創建日誌群組，生產環境中不會有任何輸出
 * 
 * @param label - 群組標籤
 * 
 * @example
 * ```typescript
 * import { devGroup, devGroupEnd, devLog } from '@/utils/devLogger';
 * 
 * devGroup('API呼叫');
 * devLog('請求URL:', url);
 * devLog('請求參數:', params);
 * devGroupEnd();
 * ```
 */
export const devGroup = (label?: string): void => {
  if (isDevelopment()) {
    console.group(label);
  }
};

/**
 * 開發環境專用console.groupEnd包裝器
 * 只在開發環境中結束日誌群組，生產環境中不會有任何輸出
 */
export const devGroupEnd = (): void => {
  if (isDevelopment()) {
    console.groupEnd();
  }
};

/**
 * 開發環境專用console.time包裝器
 * 只在開發環境中開始計時，生產環境中不會有任何輸出
 * 
 * @param label - 計時器標籤
 * 
 * @example
 * ```typescript
 * import { devTime, devTimeEnd } from '@/utils/devLogger';
 * 
 * devTime('API呼叫');
 * // ... 執行API呼叫
 * devTimeEnd('API呼叫');
 * ```
 */
export const devTime = (label?: string): void => {
  if (isDevelopment()) {
    console.time(label);
  }
};

/**
 * 開發環境專用console.timeEnd包裝器
 * 只在開發環境中結束計時，生產環境中不會有任何輸出
 * 
 * @param label - 計時器標籤
 */
export const devTimeEnd = (label?: string): void => {
  if (isDevelopment()) {
    console.timeEnd(label);
  }
};

/**
 * 預設導出devLog作為主要的日誌函數
 * 這樣可以簡化導入：import devLog from '@/utils/devLogger'
 */
export default devLog;
